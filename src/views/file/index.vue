<template>
  <div class="file-manager">
    <!-- 顶部导航栏 -->
    <div class="header-bar">
      <div class="header-left">
        <div class="logo-area">
          <i class="el-icon-folder-opened logo-icon"></i>
          <h2 class="title">文件管理器</h2>
        </div>
        <div class="quick-stats">
          <div class="stat-item">
            <span class="stat-label">总计</span>
            <span class="stat-value">{{ filteredFiles.length }}</span>
          </div>
          <div v-if="selectedFiles.length > 0" class="stat-item">
            <span class="stat-label">已选择</span>
            <span class="stat-value selected">{{ selectedFiles.length }}</span>
          </div>
        </div>
      </div>

      <div class="header-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文件或文件夹..."
          prefix-icon="el-icon-search"
          class="search-input"
          clearable
          @input="handleSearch"
        >
          <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
        </el-input>

        <div class="header-actions">
          <el-dropdown trigger="click" @command="handleSortChange">
            <el-button class="sort-btn">
              <i class="el-icon-sort"></i>
              <span>排序</span>
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="name">按名称</el-dropdown-item>
              <el-dropdown-item command="size">按大小</el-dropdown-item>
              <el-dropdown-item command="date">按时间</el-dropdown-item>
              <el-dropdown-item command="type">按类型</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

          <div class="view-switcher">
            <el-button-group>
              <el-button
                :type="viewMode === 'list' ? 'primary' : ''"
                icon="el-icon-menu"
                size="small"
                @click="viewMode = 'list'"
              />
              <el-button
                :type="viewMode === 'grid' ? 'primary' : ''"
                icon="el-icon-s-grid"
                size="small"
                @click="viewMode = 'grid'"
              />
            </el-button-group>
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content-wrapper">
      <!-- 左侧边栏 -->
      <div class="sidebar">
        <!-- 快速访问 -->
        <div class="sidebar-section">
          <h3 class="section-title">
            <i class="el-icon-star-off"></i>
            快速访问
          </h3>
          <div class="nav-list">
            <div
              class="nav-item"
              :class="{ active: currentPath.length === 0 }"
              @click="navigateToRoot"
            >
              <i class="el-icon-house"></i>
              <span>首页</span>
            </div>
            <div class="nav-item" @click="filterByType('recent')">
              <i class="el-icon-time"></i>
              <span>最近使用</span>
              <span class="count">{{ getTypeCount('recent') }}</span>
            </div>
            <div class="nav-item" @click="filterByType('image')">
              <i class="el-icon-picture-outline"></i>
              <span>图片</span>
              <span class="count">{{ getTypeCount('image') }}</span>
            </div>
            <div class="nav-item" @click="filterByType('document')">
              <i class="el-icon-document"></i>
              <span>文档</span>
              <span class="count">{{ getTypeCount('document') }}</span>
            </div>
            <div class="nav-item" @click="filterByType('video')">
              <i class="el-icon-video-play"></i>
              <span>视频</span>
              <span class="count">{{ getTypeCount('video') }}</span>
            </div>
            <div class="nav-item" @click="filterByType('audio')">
              <i class="el-icon-headset"></i>
              <span>音频</span>
              <span class="count">{{ getTypeCount('audio') }}</span>
            </div>
          </div>
        </div>

        <!-- 路径历史 -->
        <div v-if="pathHistory.length > 0" class="sidebar-section">
          <h3 class="section-title">
            <i class="el-icon-clock"></i>
            访问历史
          </h3>
          <div class="path-history">
            <div
              v-for="item in pathHistory.slice(0, 5)"
              :key="item.id"
              class="history-item"
              @click="navigateToPath(item)"
            >
              <i class="el-icon-folder"></i>
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>

        <!-- 存储信息 -->
        <div class="sidebar-section">
          <h3 class="section-title">
            <i class="el-icon-pie-chart"></i>
            存储空间
          </h3>
          <div class="storage-info">
            <div class="storage-bar">
              <div class="storage-used" style="width: 65%"></div>
            </div>
            <div class="storage-text">
              <span>已使用 25.6 GB，共 128 GB</span>
            </div>
            <div class="storage-details">
              <div class="storage-item">
                <span class="storage-dot" style="background: #409eff"></span>
                <span>文档 8.2 GB</span>
              </div>
              <div class="storage-item">
                <span class="storage-dot" style="background: #67c23a"></span>
                <span>图片 12.4 GB</span>
              </div>
              <div class="storage-item">
                <span class="storage-dot" style="background: #e6a23c"></span>
                <span>视频 5.0 GB</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作面板 -->
        <div class="sidebar-section">
          <h3 class="section-title">
            <i class="el-icon-setting"></i>
            操作
          </h3>
          <div class="action-panel">
            <el-button type="primary" size="small" class="action-btn" @click="handleUpload">
              <i class="el-icon-upload"></i>
              上传文件
            </el-button>
            <el-button size="small" class="action-btn" @click="handleCreateFolder">
              <i class="el-icon-folder-add"></i>
              新建文件夹
            </el-button>
            <el-button
              size="small"
              type="danger"
              :disabled="selectedFiles.length === 0"
              class="action-btn"
              @click="handleBatchDelete"
            >
              <i class="el-icon-delete"></i>
              批量删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 主内容区 -->
      <div class="main-content">
        <!-- 面包屑工具栏 -->
        <div class="breadcrumb-toolbar">
          <div class="navigation-area">
            <!-- 导航按钮 -->
            <div class="nav-buttons">
              <button class="nav-btn" :disabled="!canGoBack" title="后退" @click="navigateBack">
                <i class="el-icon-arrow-left"></i>
              </button>
              <button
                class="nav-btn"
                :disabled="!canGoForward"
                title="前进"
                @click="navigateForward"
              >
                <i class="el-icon-arrow-right"></i>
              </button>
              <button class="nav-btn refresh-btn" title="刷新" @click="refreshFiles">
                <i class="el-icon-refresh"></i>
              </button>
            </div>

            <!-- 面包屑导航 -->
            <div class="custom-breadcrumb">
              <el-breadcrumb separator="/">
                <el-breadcrumb-item>
                  <span class="breadcrumb-item clickable" @click="navigateToRoot">
                    <i class="el-icon-house"></i>
                    首页
                  </span>
                </el-breadcrumb-item>
                <el-breadcrumb-item v-for="(item, index) in currentPath" :key="item.id">
                  <span
                    class="breadcrumb-item"
                    :class="{ clickable: index < currentPath.length - 1 }"
                    @click="navigateToBreadcrumb(index)"
                  >
                    {{ item.name }}
                  </span>
                </el-breadcrumb-item>
              </el-breadcrumb>
            </div>
          </div>
        </div>

        <!-- 文件列表内容 -->
        <div v-loading="loading" class="content-inner">
          <!-- 网格视图 -->
          <div v-if="viewMode === 'grid'" class="grid-view">
            <transition-group name="file-item" tag="div" class="grid-container">
              <div
                v-for="file in filteredFiles"
                :key="file.id"
                class="file-card"
                :class="{
                  'is-selected': selectedFiles.includes(file.id),
                  'is-folder': file.type === 'folder',
                }"
                @click="handleFileClick(file)"
                @dblclick="handleFileDoubleClick(file)"
                @contextmenu.prevent="handleContextMenu(file, $event)"
              >
                <!-- 选择框 -->
                <div class="file-checkbox">
                  <el-checkbox
                    :value="selectedFiles.includes(file.id)"
                    @input="handleFileSelect(file.id, $event)"
                    @click.stop
                  />
                </div>

                <!-- 文件夹数量徽章 -->
                <div v-if="file.type === 'folder' && file.fileCount" class="folder-badge">
                  {{ file.fileCount }}
                </div>

                <!-- 文件图标/缩略图 -->
                <div class="file-icon">
                  <img
                    v-if="file.type === 'image' && file.thumbnail"
                    :src="file.thumbnail"
                    :alt="file.name"
                    class="file-thumbnail"
                  />
                  <i
                    v-else
                    :class="getFileIcon(file)"
                    :style="getIconStyle(file)"
                    class="file-type-icon"
                  >
                  </i>
                </div>

                <!-- 文件信息 -->
                <div class="file-info">
                  <div class="file-card-name" :title="file.name">{{ file.name }}</div>
                  <div class="file-meta">
                    <span v-if="file.type !== 'folder'" class="file-size">
                      {{ formatFileSize(file.size) }}
                    </span>
                    <span v-else-if="file.fileCount !== undefined" class="file-count">
                      {{ file.fileCount }} 项
                    </span>
                    <span class="file-date">{{ formatDateShort(file.modifiedAt) }}</span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="file-actions">
                  <el-dropdown
                    trigger="click"
                    @command="(command) => handleFileAction(command, file)"
                  >
                    <el-button type="text" size="mini" @click.stop>
                      <i class="el-icon-more"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item v-if="file.type === 'folder'" command="enter">
                        <i class="el-icon-right"></i>
                        打开
                      </el-dropdown-item>
                      <el-dropdown-item v-else command="preview">
                        <i class="el-icon-view"></i>
                        预览
                      </el-dropdown-item>
                      <el-dropdown-item command="download">
                        <i class="el-icon-download"></i>
                        下载
                      </el-dropdown-item>
                      <el-dropdown-item command="rename">
                        <i class="el-icon-edit"></i>
                        重命名
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" class="danger-item">
                        <i class="el-icon-delete"></i>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
            </transition-group>
          </div>

          <!-- 列表视图 -->
          <div v-else class="list-view">
            <el-table
              :data="filteredFiles"
              :row-class-name="getRowClassName"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              @row-dblclick="handleFileDoubleClick"
            >
              <el-table-column type="selection" width="50" />

              <el-table-column label="名称" min-width="300">
                <template slot-scope="scope">
                  <div class="file-name-cell">
                    <i
                      :class="getFileIcon(scope.row)"
                      :style="getIconStyle(scope.row)"
                      class="file-icon-small"
                    >
                    </i>
                    <img
                      v-if="scope.row.type === 'image' && scope.row.thumbnail"
                      :src="scope.row.thumbnail"
                      class="file-thumbnail-small"
                    />
                    <span
                      class="file-name"
                      :class="{ 'is-folder': scope.row.type === 'folder' }"
                      @dblclick="handleFileDoubleClick(scope.row)"
                    >
                      {{ scope.row.name }}
                    </span>
                    <span
                      v-if="scope.row.type === 'folder' && scope.row.fileCount"
                      class="file-count-badge"
                    >
                      ({{ scope.row.fileCount }})
                    </span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="大小" width="120">
                <template slot-scope="scope">
                  <span v-if="scope.row.type !== 'folder'">
                    {{ formatFileSize(scope.row.size) }}
                  </span>
                  <span v-else class="folder-indicator">-</span>
                </template>
              </el-table-column>

              <el-table-column label="类型" width="120">
                <template slot-scope="scope">
                  {{ getFileDescription(scope.row) }}
                </template>
              </el-table-column>

              <el-table-column label="修改时间" width="160">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.modifiedAt) }}
                </template>
              </el-table-column>

              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <div class="action-buttons">
                    <el-button
                      v-if="scope.row.type === 'folder'"
                      type="text"
                      size="mini"
                      title="打开文件夹"
                      @click="enterFolder(scope.row)"
                    >
                      <i class="el-icon-right"></i>
                    </el-button>
                    <el-button
                      v-else
                      type="text"
                      size="mini"
                      title="预览"
                      @click="handlePreview(scope.row)"
                    >
                      <i class="el-icon-view"></i>
                    </el-button>
                    <el-button
                      type="text"
                      size="mini"
                      title="重命名"
                      @click="handleRename(scope.row)"
                    >
                      <i class="el-icon-edit"></i>
                    </el-button>
                    <el-button
                      type="text"
                      size="mini"
                      class="delete-btn"
                      title="删除"
                      @click="handleDelete(scope.row)"
                    >
                      <i class="el-icon-delete"></i>
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredFiles.length === 0" class="empty-state">
            <i class="el-icon-folder-opened empty-icon"></i>
            <p class="empty-text">此文件夹为空</p>
            <div class="empty-actions">
              <el-button type="primary" @click="handleUpload">
                <i class="el-icon-upload"></i>
                上传文件
              </el-button>
              <el-button @click="handleCreateFolder">
                <i class="el-icon-folder-add"></i>
                新建文件夹
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      title="上传文件"
      :visible.sync="uploadDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-upload
        ref="upload"
        :file-list="uploadFileList"
        :http-request="handleFileUpload"
        :before-upload="beforeUpload"
        :on-preview="handlePreviewUpload"
        :on-remove="handleRemoveUpload"
        :auto-upload="false"
        multiple
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div slot="tip" class="el-upload__tip">支持多文件上传，单文件不超过100MB</div>
      </el-upload>

      <div slot="footer" class="dialog-footer">
        <el-button @click="uploadDialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="uploadFileList.length === 0" @click="confirmUpload">
          确定上传
        </el-button>
      </div>
    </el-dialog>

    <!-- 新建文件夹对话框 -->
    <el-dialog
      title="新建文件夹"
      :visible.sync="newFolderDialogVisible"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form>
        <el-form-item label="文件夹名称">
          <el-input
            ref="folderNameInput"
            v-model="newFolderName"
            placeholder="请输入文件夹名称"
            @keyup.enter.native="confirmCreateFolder"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="newFolderDialogVisible = false">取 消</el-button>
        <el-button type="primary" :disabled="!newFolderName.trim()" @click="confirmCreateFolder">
          创 建
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'FileManager',
  data() {
    return {
      viewMode: 'grid', // grid 或 list
      searchKeyword: '',
      sortBy: 'name',
      filterType: '',
      loading: false,
      selectedFiles: [],

      // 导航相关
      currentPath: [], // 当前路径 [{ id, name, parentId }, ...]
      navigationHistory: [[]], // 导航历史
      currentHistoryIndex: 0,
      pathHistory: [], // 路径访问历史

      // 对话框状态
      uploadDialogVisible: false,
      newFolderDialogVisible: false,
      uploadFileList: [],
      newFolderName: '',

      // 所有文件数据
      allFiles: [
        // 根目录文件
        {
          id: 1,
          name: '工作文档',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-15 09:30:00',
          fileCount: 8,
          parentId: null,
        },
        {
          id: 2,
          name: '图片收藏',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-14 16:20:00',
          fileCount: 15,
          parentId: null,
        },
        {
          id: 3,
          name: '音乐收藏',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-13 20:15:00',
          fileCount: 12,
          parentId: null,
        },
        {
          id: 4,
          name: '项目代码',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-12 14:45:00',
          fileCount: 25,
          parentId: null,
        },
        {
          id: 5,
          name: '重要合同.pdf',
          type: 'document',
          size: 2048000,
          modifiedAt: '2024-01-15 10:30:00',
          parentId: null,
        },
        {
          id: 6,
          name: '年度报告.docx',
          type: 'document',
          size: 5120000,
          modifiedAt: '2024-01-14 14:20:00',
          parentId: null,
        },
        {
          id: 7,
          name: '产品演示.pptx',
          type: 'document',
          size: 15360000,
          modifiedAt: '2024-01-13 11:45:00',
          parentId: null,
        },
        {
          id: 8,
          name: '培训视频.mp4',
          type: 'video',
          size: 102400000,
          modifiedAt: '2024-01-12 16:30:00',
          parentId: null,
        },

        // 工作文档文件夹内容 (parentId: 1)
        {
          id: 101,
          name: '项目计划书.docx',
          type: 'document',
          size: 1024000,
          modifiedAt: '2024-01-15 09:30:00',
          parentId: 1,
        },
        {
          id: 102,
          name: '需求分析.pdf',
          type: 'document',
          size: 2048000,
          modifiedAt: '2024-01-14 15:20:00',
          parentId: 1,
        },
        {
          id: 103,
          name: '会议纪要',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-13 14:20:00',
          fileCount: 3,
          parentId: 1,
        },
        {
          id: 104,
          name: '财务报表.xlsx',
          type: 'spreadsheet',
          size: 512000,
          modifiedAt: '2024-01-13 10:15:00',
          parentId: 1,
        },
        {
          id: 105,
          name: '团队架构图.png',
          type: 'image',
          size: 256000,
          modifiedAt: '2024-01-12 16:45:00',
          parentId: 1,
        },
        {
          id: 106,
          name: '工作日志.txt',
          type: 'document',
          size: 8192,
          modifiedAt: '2024-01-12 09:00:00',
          parentId: 1,
        },
        {
          id: 107,
          name: '客户反馈.docx',
          type: 'document',
          size: 768000,
          modifiedAt: '2024-01-11 17:30:00',
          parentId: 1,
        },
        {
          id: 108,
          name: '版本说明.md',
          type: 'document',
          size: 4096,
          modifiedAt: '2024-01-11 14:20:00',
          parentId: 1,
        },

        // 图片收藏文件夹内容 (parentId: 2)
        {
          id: 201,
          name: '旅行照片',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-14 16:20:00',
          fileCount: 8,
          parentId: 2,
        },
        {
          id: 202,
          name: '家庭聚会',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-13 18:30:00',
          fileCount: 4,
          parentId: 2,
        },
        {
          id: 203,
          name: '风景-01.jpg',
          type: 'image',
          size: 3456000,
          modifiedAt: '2024-01-14 11:15:00',
          thumbnail: 'https://via.placeholder.com/300x200/3498DB/fff?text=Landscape+1',
          parentId: 2,
        },
        {
          id: 204,
          name: '风景-02.jpg',
          type: 'image',
          size: 2876000,
          modifiedAt: '2024-01-14 10:22:00',
          thumbnail: 'https://via.placeholder.com/300x200/E74C3C/fff?text=Landscape+2',
          parentId: 2,
        },
        {
          id: 205,
          name: '人物-01.jpg',
          type: 'image',
          size: 2245000,
          modifiedAt: '2024-01-13 15:30:00',
          thumbnail: 'https://via.placeholder.com/300x200/9B59B6/fff?text=Portrait+1',
          parentId: 2,
        },
        {
          id: 206,
          name: '城市夜景.jpg',
          type: 'image',
          size: 4125000,
          modifiedAt: '2024-01-13 12:45:00',
          thumbnail: 'https://via.placeholder.com/300x200/F39C12/fff?text=City+Night',
          parentId: 2,
        },
        {
          id: 207,
          name: '美食拍摄.jpg',
          type: 'image',
          size: 1876000,
          modifiedAt: '2024-01-12 19:20:00',
          thumbnail: 'https://via.placeholder.com/300x200/27AE60/fff?text=Food',
          parentId: 2,
        },
        {
          id: 208,
          name: '宠物照片.jpg',
          type: 'image',
          size: 1456000,
          modifiedAt: '2024-01-12 08:30:00',
          thumbnail: 'https://via.placeholder.com/300x200/E67E22/fff?text=Pet',
          parentId: 2,
        },
        {
          id: 209,
          name: '建筑摄影.jpg',
          type: 'image',
          size: 3789000,
          modifiedAt: '2024-01-11 16:45:00',
          thumbnail: 'https://via.placeholder.com/300x200/8E44AD/fff?text=Architecture',
          parentId: 2,
        },

        // 音乐收藏文件夹内容 (parentId: 3)
        {
          id: 301,
          name: '流行音乐',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-13 09:15:00',
          fileCount: 5,
          parentId: 3,
        },
        {
          id: 302,
          name: '古典音乐',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-12 18:30:00',
          fileCount: 3,
          parentId: 3,
        },
        {
          id: 303,
          name: '最爱单曲.mp3',
          type: 'audio',
          size: 5120000,
          modifiedAt: '2024-01-13 20:15:00',
          parentId: 3,
        },
        {
          id: 304,
          name: '播放列表.m3u',
          type: 'audio',
          size: 2048,
          modifiedAt: '2024-01-12 19:45:00',
          parentId: 3,
        },
        {
          id: 305,
          name: '轻音乐集合.mp3',
          type: 'audio',
          size: 7680000,
          modifiedAt: '2024-01-12 15:20:00',
          parentId: 3,
        },
        {
          id: 306,
          name: '摇滚经典.mp3',
          type: 'audio',
          size: 6144000,
          modifiedAt: '2024-01-11 20:10:00',
          parentId: 3,
        },
        {
          id: 307,
          name: '世界民谣.mp3',
          type: 'audio',
          size: 4608000,
          modifiedAt: '2024-01-11 14:30:00',
          parentId: 3,
        },
        {
          id: 308,
          name: '电子音乐.mp3',
          type: 'audio',
          size: 5632000,
          modifiedAt: '2024-01-10 18:15:00',
          parentId: 3,
        },
        {
          id: 309,
          name: 'Jazz精选.mp3',
          type: 'audio',
          size: 8192000,
          modifiedAt: '2024-01-10 12:45:00',
          parentId: 3,
        },

        // 项目代码文件夹内容 (parentId: 4)
        {
          id: 401,
          name: 'vue-project',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-12 14:45:00',
          fileCount: 15,
          parentId: 4,
        },
        {
          id: 402,
          name: 'react-app',
          type: 'folder',
          size: 0,
          modifiedAt: '2024-01-11 16:20:00',
          fileCount: 10,
          parentId: 4,
        },
        {
          id: 403,
          name: 'package.json',
          type: 'code',
          size: 1024,
          modifiedAt: '2024-01-12 14:30:00',
          parentId: 4,
        },
        {
          id: 404,
          name: 'README.md',
          type: 'document',
          size: 3072,
          modifiedAt: '2024-01-12 14:45:00',
          parentId: 4,
        },

        // 项目代码文件夹内容 (parentId: 4) - 继续
        {
          id: 405,
          name: 'webpack.config.js',
          type: 'code',
          size: 2048,
          modifiedAt: '2024-01-12 11:30:00',
          parentId: 4,
        },
        {
          id: 406,
          name: 'main.js',
          type: 'code',
          size: 4096,
          modifiedAt: '2024-01-12 10:15:00',
          parentId: 4,
        },
        {
          id: 407,
          name: 'styles.css',
          type: 'code',
          size: 8192,
          modifiedAt: '2024-01-11 17:45:00',
          parentId: 4,
        },
        {
          id: 408,
          name: 'utils.js',
          type: 'code',
          size: 6144,
          modifiedAt: '2024-01-11 15:20:00',
          parentId: 4,
        },
        {
          id: 409,
          name: 'api.js',
          type: 'code',
          size: 3072,
          modifiedAt: '2024-01-11 14:10:00',
          parentId: 4,
        },

        // 会议纪要文件夹内容 (parentId: 103)
        {
          id: 1031,
          name: '2024年1月会议.docx',
          type: 'document',
          size: 128000,
          modifiedAt: '2024-01-13 14:20:00',
          parentId: 103,
        },
        {
          id: 1032,
          name: '项目讨论记录.txt',
          type: 'document',
          size: 4096,
          modifiedAt: '2024-01-12 10:15:00',
          parentId: 103,
        },
        {
          id: 1033,
          name: '决策要点.md',
          type: 'document',
          size: 2048,
          modifiedAt: '2024-01-11 16:30:00',
          parentId: 103,
        },

        // 旅行照片文件夹内容 (parentId: 201)
        {
          id: 2011,
          name: '北京-天安门.jpg',
          type: 'image',
          size: 2048000,
          modifiedAt: '2024-01-14 16:20:00',
          thumbnail: 'https://via.placeholder.com/300x200/F39C12/fff?text=Beijing+1',
          parentId: 201,
        },
        {
          id: 2012,
          name: '北京-故宫.jpg',
          type: 'image',
          size: 1856000,
          modifiedAt: '2024-01-14 16:22:00',
          thumbnail: 'https://via.placeholder.com/300x200/E67E22/fff?text=Beijing+2',
          parentId: 201,
        },
        {
          id: 2013,
          name: '上海-外滩.jpg',
          type: 'image',
          size: 2256000,
          modifiedAt: '2024-01-13 14:10:00',
          thumbnail: 'https://via.placeholder.com/300x200/3498DB/fff?text=Shanghai+1',
          parentId: 201,
        },
        {
          id: 2014,
          name: '杭州-西湖.jpg',
          type: 'image',
          size: 1945000,
          modifiedAt: '2024-01-12 11:30:00',
          thumbnail: 'https://via.placeholder.com/300x200/1ABC9C/fff?text=Hangzhou',
          parentId: 201,
        },
        {
          id: 2015,
          name: '深圳-海滨.jpg',
          type: 'image',
          size: 2156000,
          modifiedAt: '2024-01-11 09:20:00',
          thumbnail: 'https://via.placeholder.com/300x200/E74C3C/fff?text=Shenzhen',
          parentId: 201,
        },
        {
          id: 2016,
          name: '成都-宽窄巷子.jpg',
          type: 'image',
          size: 1768000,
          modifiedAt: '2024-01-10 14:45:00',
          thumbnail: 'https://via.placeholder.com/300x200/9B59B6/fff?text=Chengdu',
          parentId: 201,
        },
        {
          id: 2017,
          name: '西安-兵马俑.jpg',
          type: 'image',
          size: 2345000,
          modifiedAt: '2024-01-09 16:30:00',
          thumbnail: 'https://via.placeholder.com/300x200/34495E/fff?text=Xian',
          parentId: 201,
        },
        {
          id: 2018,
          name: '厦门-鼓浪屿.jpg',
          type: 'image',
          size: 1892000,
          modifiedAt: '2024-01-08 12:15:00',
          thumbnail: 'https://via.placeholder.com/300x200/16A085/fff?text=Xiamen',
          parentId: 201,
        },

        // 家庭聚会文件夹内容 (parentId: 202)
        {
          id: 2021,
          name: '春节聚餐.jpg',
          type: 'image',
          size: 1645000,
          modifiedAt: '2024-01-13 12:15:00',
          thumbnail: 'https://via.placeholder.com/300x200/E74C3C/fff?text=New+Year',
          parentId: 202,
        },
        {
          id: 2022,
          name: '生日派对.jpg',
          type: 'image',
          size: 1987000,
          modifiedAt: '2024-01-12 18:20:00',
          thumbnail: 'https://via.placeholder.com/300x200/F39C12/fff?text=Birthday',
          parentId: 202,
        },
        {
          id: 2023,
          name: '全家福.jpg',
          type: 'image',
          size: 2234000,
          modifiedAt: '2024-01-11 15:30:00',
          thumbnail: 'https://via.placeholder.com/300x200/27AE60/fff?text=Family',
          parentId: 202,
        },
        {
          id: 2024,
          name: '周末聚会.jpg',
          type: 'image',
          size: 1756000,
          modifiedAt: '2024-01-10 19:45:00',
          thumbnail: 'https://via.placeholder.com/300x200/3498DB/fff?text=Weekend',
          parentId: 202,
        },

        // 流行音乐文件夹内容 (parentId: 301)
        {
          id: 3011,
          name: '热门单曲 - 歌手A.mp3',
          type: 'audio',
          size: 4096000,
          modifiedAt: '2024-01-13 09:15:00',
          parentId: 301,
        },
        {
          id: 3012,
          name: '经典老歌 - 歌手B.mp3',
          type: 'audio',
          size: 3584000,
          modifiedAt: '2024-01-12 20:30:00',
          parentId: 301,
        },
        {
          id: 3013,
          name: '新歌推荐 - 歌手C.mp3',
          type: 'audio',
          size: 4608000,
          modifiedAt: '2024-01-11 14:20:00',
          parentId: 301,
        },
        {
          id: 3014,
          name: '抒情歌曲 - 歌手D.mp3',
          type: 'audio',
          size: 3072000,
          modifiedAt: '2024-01-10 16:45:00',
          parentId: 301,
        },
        {
          id: 3015,
          name: '节拍强劲 - 歌手E.mp3',
          type: 'audio',
          size: 5120000,
          modifiedAt: '2024-01-09 12:30:00',
          parentId: 301,
        },

        // 古典音乐文件夹内容 (parentId: 302)
        {
          id: 3021,
          name: '贝多芬 - 第九交响曲.mp3',
          type: 'audio',
          size: 12288000,
          modifiedAt: '2024-01-12 18:30:00',
          parentId: 302,
        },
        {
          id: 3022,
          name: '莫扎特 - 安魂曲.mp3',
          type: 'audio',
          size: 10240000,
          modifiedAt: '2024-01-11 15:45:00',
          parentId: 302,
        },
        {
          id: 3023,
          name: '肖邦 - 夜曲合集.mp3',
          type: 'audio',
          size: 8192000,
          modifiedAt: '2024-01-10 20:15:00',
          parentId: 302,
        },
      ],
    };
  },

  computed: {
    // 当前显示的文件
    files() {
      const currentFolderId =
        this.currentPath.length > 0 ? this.currentPath[this.currentPath.length - 1].id : null;

      return this.allFiles.filter((file) => file.parentId === currentFolderId);
    },

    // 过滤后的文件
    filteredFiles() {
      let filtered = [...this.files];

      // 搜索过滤
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase();
        filtered = filtered.filter((file) => file.name.toLowerCase().includes(keyword));
      }

      // 类型过滤
      if (this.filterType && this.filterType !== 'recent') {
        filtered = filtered.filter((file) => file.type === this.filterType);
      }

      // 排序
      filtered.sort((a, b) => {
        // 文件夹始终在前面
        if (a.type === 'folder' && b.type !== 'folder') return -1;
        if (a.type !== 'folder' && b.type === 'folder') return 1;

        switch (this.sortBy) {
          case 'name':
            return a.name.localeCompare(b.name);
          case 'size':
            return b.size - a.size;
          case 'date':
            return new Date(b.modifiedAt) - new Date(a.modifiedAt);
          case 'type':
            return a.type.localeCompare(b.type);
          default:
            return 0;
        }
      });

      return filtered;
    },

    // 导航状态
    canGoBack() {
      return this.currentHistoryIndex > 0;
    },

    canGoForward() {
      return this.currentHistoryIndex < this.navigationHistory.length - 1;
    },
  },

  mounted() {
    this.refreshFiles();
  },

  methods: {
    // === 导航相关方法 ===

    // 进入文件夹
    enterFolder(folder) {
      const newPath = [
        ...this.currentPath,
        {
          id: folder.id,
          name: folder.name,
          parentId: folder.parentId,
        },
      ];

      this.navigateToPath(newPath);
      this.addToPathHistory(folder);
    },

    // 导航到指定路径
    navigateToPath(path) {
      this.currentPath = [...path];

      // 更新导航历史
      const newHistory = this.navigationHistory.slice(0, this.currentHistoryIndex + 1);
      newHistory.push([...path]);
      this.navigationHistory = newHistory;
      this.currentHistoryIndex = newHistory.length - 1;

      this.selectedFiles = [];
      this.refreshFiles();
    },

    // 导航到根目录
    navigateToRoot() {
      this.navigateToPath([]);
    },

    // 面包屑导航
    navigateToBreadcrumb(index) {
      if (index < this.currentPath.length - 1) {
        const newPath = this.currentPath.slice(0, index + 1);
        this.navigateToPath(newPath);
      }
    },

    // 后退
    navigateBack() {
      if (this.canGoBack) {
        this.currentHistoryIndex--;
        this.currentPath = [...this.navigationHistory[this.currentHistoryIndex]];
        this.selectedFiles = [];
        this.refreshFiles();
      }
    },

    // 前进
    navigateForward() {
      if (this.canGoForward) {
        this.currentHistoryIndex++;
        this.currentPath = [...this.navigationHistory[this.currentHistoryIndex]];
        this.selectedFiles = [];
        this.refreshFiles();
      }
    },

    // 添加到路径历史
    addToPathHistory(folder) {
      const historyItem = {
        id: folder.id,
        name: folder.name,
        path: [...this.currentPath],
      };

      // 移除重复项
      this.pathHistory = this.pathHistory.filter((item) => item.id !== folder.id);

      // 添加到开头
      this.pathHistory.unshift(historyItem);

      // 限制历史记录数量
      if (this.pathHistory.length > 10) {
        this.pathHistory = this.pathHistory.slice(0, 10);
      }
    },

    // === 文件操作方法 ===

    // 文件点击
    handleFileClick(file) {
      if (event.ctrlKey || event.metaKey) {
        // 多选
        this.toggleFileSelection(file.id);
      } else {
        // 单选
        this.selectedFiles = [file.id];
      }
    },

    // 文件双击
    handleFileDoubleClick(file) {
      if (file.type === 'folder') {
        this.enterFolder(file);
      } else {
        this.handlePreview(file);
      }
    },

    // 文件选择
    handleFileSelect(fileId, checked) {
      if (checked) {
        if (!this.selectedFiles.includes(fileId)) {
          this.selectedFiles.push(fileId);
        }
      } else {
        this.selectedFiles = this.selectedFiles.filter((id) => id !== fileId);
      }
    },

    // 切换文件选择状态
    toggleFileSelection(fileId) {
      if (this.selectedFiles.includes(fileId)) {
        this.selectedFiles = this.selectedFiles.filter((id) => id !== fileId);
      } else {
        this.selectedFiles.push(fileId);
      }
    },

    // 表格选择变化
    handleSelectionChange(selection) {
      this.selectedFiles = selection.map((file) => file.id);
    },

    // 获取行类名
    getRowClassName({ row }) {
      return row.type === 'folder' ? 'folder-row' : '';
    },

    // === 文件操作 ===

    // 文件动作处理
    handleFileAction(command, file) {
      switch (command) {
        case 'enter':
          this.enterFolder(file);
          break;
        case 'preview':
          this.handlePreview(file);
          break;
        case 'download':
          this.handleDownload(file);
          break;
        case 'rename':
          this.handleRename(file);
          break;
        case 'delete':
          this.handleDelete(file);
          break;
      }
    },

    // 预览文件
    handlePreview(file) {
      this.$message({
        message: `正在预览: ${file.name}`,
        type: 'info',
      });
    },

    // 下载文件
    handleDownload(file) {
      this.$message({
        message: `正在下载: ${file.name}`,
        type: 'success',
      });
    },

    // 重命名文件
    handleRename(file) {
      this.$prompt('请输入新的文件名', '重命名', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: file.name,
      })
        .then(({ value }) => {
          if (value && value.trim() && value.trim() !== file.name) {
            file.name = value.trim();
            this.$message({
              type: 'success',
              message: '重命名成功',
            });
          }
        })
        .catch(() => {});
    },

    // 删除文件
    handleDelete(file) {
      this.$confirm(`确定要删除 "${file.name}" 吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          const index = this.allFiles.findIndex((f) => f.id === file.id);
          if (index > -1) {
            this.allFiles.splice(index, 1);
            this.selectedFiles = this.selectedFiles.filter((id) => id !== file.id);
            this.$message({
              type: 'success',
              message: '删除成功',
            });
          }
        })
        .catch(() => {});
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedFiles.length === 0) return;

      this.$confirm(`确定要删除选中的 ${this.selectedFiles.length} 个项目吗？`, '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.allFiles = this.allFiles.filter((file) => !this.selectedFiles.includes(file.id));
          this.selectedFiles = [];
          this.$message({
            type: 'success',
            message: '批量删除成功',
          });
        })
        .catch(() => {});
    },

    // === 上传相关 ===

    // 上传文件
    handleUpload() {
      this.uploadDialogVisible = true;
      this.uploadFileList = [];
    },

    // 上传前检查
    beforeUpload(file) {
      const isLt100M = file.size / 1024 / 1024 < 100;
      if (!isLt100M) {
        this.$message.error('文件大小不能超过 100MB!');
        return false;
      }
      return true;
    },

    // 自定义上传
    handleFileUpload(options) {
      // 这里应该调用实际的上传 API
      console.log('上传文件:', options.file);
    },

    // 预览上传文件
    handlePreviewUpload(file) {
      console.log('预览上传文件:', file);
    },

    // 移除上传文件
    handleRemoveUpload(file, fileList) {
      this.uploadFileList = fileList;
    },

    // 确认上传
    confirmUpload() {
      this.$refs.upload.submit();

      // 模拟上传成功
      setTimeout(() => {
        const currentFolderId =
          this.currentPath.length > 0 ? this.currentPath[this.currentPath.length - 1].id : null;

        this.uploadFileList.forEach((file) => {
          const newFile = {
            id: Date.now() + Math.random(),
            name: file.name,
            type: this.getFileTypeFromName(file.name),
            size: file.size,
            modifiedAt: new Date().toISOString().replace('T', ' ').slice(0, 19),
            parentId: currentFolderId,
          };
          this.allFiles.push(newFile);
        });

        this.uploadDialogVisible = false;
        this.uploadFileList = [];
        this.$message.success('上传成功');
      }, 1000);
    },

    // === 文件夹操作 ===

    // 新建文件夹
    handleCreateFolder() {
      this.newFolderDialogVisible = true;
      this.newFolderName = '';
      this.$nextTick(() => {
        this.$refs.folderNameInput && this.$refs.folderNameInput.focus();
      });
    },

    // 确认创建文件夹
    confirmCreateFolder() {
      if (!this.newFolderName.trim()) return;

      const currentFolderId =
        this.currentPath.length > 0 ? this.currentPath[this.currentPath.length - 1].id : null;

      const newFolder = {
        id: Date.now() + Math.random(),
        name: this.newFolderName.trim(),
        type: 'folder',
        size: 0,
        modifiedAt: new Date().toISOString().replace('T', ' ').slice(0, 19),
        fileCount: 0,
        parentId: currentFolderId,
      };

      this.allFiles.push(newFolder);
      this.newFolderDialogVisible = false;
      this.newFolderName = '';

      this.$message.success('文件夹创建成功');
    },

    // === 工具方法 ===

    // 刷新文件列表
    refreshFiles() {
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 300);
    },

    // 搜索处理
    handleSearch() {
      // 搜索逻辑在 computed 中处理
    },

    // 排序变化
    handleSortChange(sortBy) {
      this.sortBy = sortBy;
    },

    // 类型过滤
    filterByType(type) {
      this.filterType = this.filterType === type ? '' : type;
    },

    // 右键菜单
    handleContextMenu(file, event) {
      // 这里可以实现自定义右键菜单
      console.log('右键菜单:', file, event);
    },

    // 获取文件类型统计
    getTypeCount(type) {
      if (type === 'recent') {
        // 获取最近7天的文件
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        return this.allFiles.filter((file) => new Date(file.modifiedAt) > sevenDaysAgo).length;
      }
      return this.allFiles.filter((file) => file.type === type).length;
    },

    // 从文件名获取文件类型
    getFileTypeFromName(fileName) {
      const ext = fileName.split('.').pop().toLowerCase();
      const typeMap = {
        // 图片
        jpg: 'image',
        jpeg: 'image',
        png: 'image',
        gif: 'image',
        bmp: 'image',
        webp: 'image',
        svg: 'image',
        // 文档
        doc: 'document',
        docx: 'document',
        pdf: 'document',
        txt: 'document',
        md: 'document',
        xls: 'spreadsheet',
        xlsx: 'spreadsheet',
        ppt: 'document',
        pptx: 'document',
        // 视频
        mp4: 'video',
        avi: 'video',
        mov: 'video',
        wmv: 'video',
        flv: 'video',
        mkv: 'video',
        // 音频
        mp3: 'audio',
        wav: 'audio',
        flac: 'audio',
        aac: 'audio',
        m4a: 'audio',
        // 代码
        js: 'code',
        css: 'code',
        html: 'code',
        vue: 'code',
        py: 'code',
        java: 'code',
        // 压缩包
        zip: 'archive',
        rar: 'archive',
        '7z': 'archive',
        tar: 'archive',
        gz: 'archive',
      };
      return typeMap[ext] || 'document';
    },

    // 获取文件图标
    getFileIcon(file) {
      const iconMap = {
        folder: 'el-icon-folder',
        image: 'el-icon-picture-outline',
        document: 'el-icon-document',
        spreadsheet: 'el-icon-s-grid',
        video: 'el-icon-video-play',
        audio: 'el-icon-headset',
        code: 'el-icon-document-copy',
        archive: 'el-icon-box',
      };
      return iconMap[file.type] || 'el-icon-document';
    },

    // 获取图标颜色
    getIconStyle(file) {
      const colorMap = {
        folder: { color: '#f39c12' },
        image: { color: '#e74c3c' },
        document: { color: '#3498db' },
        spreadsheet: { color: '#27ae60' },
        video: { color: '#9b59b6' },
        audio: { color: '#e67e22' },
        code: { color: '#34495e' },
        archive: { color: '#95a5a6' },
      };
      return colorMap[file.type] || { color: '#7f8c8d' };
    },

    // 获取文件描述
    getFileDescription(file) {
      const descMap = {
        folder: '文件夹',
        image: '图片',
        document: '文档',
        spreadsheet: '表格',
        video: '视频',
        audio: '音频',
        code: '代码',
        archive: '压缩包',
      };
      return descMap[file.type] || '文件';
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },

    // 格式化日期
    formatDate(dateString) {
      const date = new Date(dateString);
      return (
        date.toLocaleDateString() +
        ' ' +
        date.toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
        })
      );
    },

    // 格式化日期(简短)
    formatDateShort(dateString) {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 1) return '今天';
      if (diffDays === 2) return '昨天';
      if (diffDays <= 7) return `${diffDays - 1}天前`;

      return date.toLocaleDateString();
    },
  },
};
</script>

<style scoped>
/* === 基础布局样式 === */
.file-manager {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* === 顶部导航栏 === */
.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  font-size: 24px;
  color: #409eff;
}

.title {
  margin: 0;
  font-size: 20px;
  color: #303133;
  font-weight: 600;
}

.quick-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(64, 158, 255, 0.1);
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

.stat-value.selected {
  color: #f56c6c;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 300px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  background: white;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sort-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

/* === 主内容区域 === */
.content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* === 左侧边栏 === */
.sidebar {
  width: 260px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-section:last-child {
  border-bottom: none;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.section-title i {
  color: #909399;
}

/* 快捷访问样式 */
.quick-access .el-button {
  width: 100%;
  justify-content: flex-start;
  margin-bottom: 8px;
  padding: 10px 12px;
  border: 1px solid transparent;
  background: transparent;
  color: #606266;
  text-align: left;
  transition: all 0.3s ease;
}

.quick-access .el-button:hover {
  background: rgba(64, 158, 255, 0.1);
  border-color: #409eff;
  color: #409eff;
  transform: translateX(4px);
}

.quick-access .el-button.is-active {
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  color: white;
  box-shadow: 0 4px 10px rgba(64, 158, 255, 0.3);
}

/* 文件类型过滤器 */
.filter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin-bottom: 6px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.filter-item:hover {
  background: rgba(64, 158, 255, 0.05);
  border-color: rgba(64, 158, 255, 0.2);
  transform: translateX(2px);
}

.filter-item.active {
  background: linear-gradient(135deg, #409eff 0%, #36a3f7 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.filter-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-icon {
  font-size: 16px;
}

.filter-name {
  font-size: 14px;
}

.filter-count {
  background: rgba(255, 255, 255, 0.3);
  color: inherit;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

.filter-item.active .filter-count {
  background: rgba(255, 255, 255, 0.2);
}

/* 存储信息 */
.storage-info {
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  margin-top: 8px;
}

.storage-used {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.storage-bar {
  height: 6px;
  background: #e4e7ed;
  border-radius: 3px;
  overflow: hidden;
}

.storage-progress {
  height: 100%;
  background: linear-gradient(90deg, #67c23a 0%, #85ce61 100%);
  border-radius: 3px;
  transition: width 0.5s ease;
  width: 65%;
}

/* === 主内容区域 === */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

/* 面包屑工具栏 */
.breadcrumb-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.navigation-area {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-buttons {
  display: flex;
  gap: 4px;
}

.nav-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background: white;
  color: #606266;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover:not(.disabled) {
  background: #409eff;
  border-color: #409eff;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
}

.nav-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 面包屑导航 */
.custom-breadcrumb {
  display: flex;
  align-items: center;
  margin-left: 12px;
  font-size: 14px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  color: #606266;
  transition: all 0.3s ease;
}

.breadcrumb-item:not(.current) {
  cursor: pointer;
}

.breadcrumb-item:not(.current):hover {
  color: #409eff;
}

.breadcrumb-item.current {
  color: #303133;
  font-weight: 600;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #c0c4cc;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar .el-button {
  padding: 8px 12px;
}

.action-btn {
  border: 1px solid #dcdfe6;
  background: white;
  transition: all 0.3s ease;
}

.action-btn:hover {
  border-color: #409eff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(64, 158, 255, 0.2);
}

.delete-btn:hover {
  border-color: #f56c6c;
  color: #f56c6c;
}

.refresh-btn {
  padding: 6px 8px;
  border: 1px solid #dcdfe6;
  background: white;
  color: #606266;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
  color: #409eff;
  transform: rotate(180deg);
}

/* === 文件内容区域 === */
.content-inner {
  flex: 1;
  padding: 20px;
  overflow: auto;
}

/* 网格视图样式 */
.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 4px;
}

.file-card {
  background: white;
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.file-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.file-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #409eff;
}

.file-card:hover::before {
  opacity: 1;
}

.file-card.is-selected {
  border-color: #409eff;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.1) 0%, rgba(64, 158, 255, 0.05) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(64, 158, 255, 0.2);
}

.file-card.is-folder:hover {
  border-color: #f39c12;
}

.file-card.is-folder.is-selected {
  border-color: #f39c12;
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.1) 0%, rgba(243, 156, 18, 0.05) 100%);
  box-shadow: 0 6px 20px rgba(243, 156, 18, 0.2);
}

/* 文件图标区域 */
.file-icon-area {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  margin-bottom: 12px;
  position: relative;
  z-index: 2;
}

/* 文件夹图标 */
.folder-icon {
  font-size: 48px;
  color: #f39c12;
  transition: all 0.3s ease;
}

.file-card:hover .folder-icon {
  transform: scale(1.1);
  color: #e67e22;
}

/* 文件图标 */
.file-icon {
  font-size: 40px;
  transition: all 0.3s ease;
}

.file-card:hover .file-icon {
  transform: scale(1.1);
}

/* 图片缩略图 */
.file-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  border: 2px solid #f5f5f5;
  transition: all 0.3s ease;
}

.file-card:hover .file-thumbnail {
  transform: scale(1.05);
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 文件信息 */
.file-info {
  text-align: center;
  position: relative;
  z-index: 2;
}

.file-name {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
  word-break: break-all;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.file-name.is-folder {
  color: #e67e22;
  font-weight: 600;
  cursor: pointer;
}

.file-name.is-folder:hover {
  color: #d35400;
  text-decoration: underline;
}

.file-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.file-size {
  font-weight: 500;
}

.file-date {
  opacity: 0.8;
}

/* 文件夹文件数量 */
.folder-count {
  background: rgba(243, 156, 18, 0.1);
  color: #f39c12;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
}

/* 选择框 */
.file-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.file-card:hover .file-checkbox {
  opacity: 1;
}

.file-card.is-selected .file-checkbox {
  opacity: 1;
}

/* 操作按钮 */
.file-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 10;
}

.file-card:hover .file-actions {
  opacity: 1;
}

.action-buttons {
  display: flex;
  gap: 4px;
}

.action-buttons .el-button {
  padding: 4px 6px;
  font-size: 12px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  color: #606266;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.action-buttons .el-button:hover {
  background: #409eff;
  color: white;
  transform: scale(1.1);
}

.action-buttons .delete-btn:hover {
  background: #f56c6c;
  color: white;
}

/* === 表格视图样式 === */
.files-table {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.files-table .el-table__header-wrapper {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.files-table .el-table__header th {
  background: transparent;
  color: #303133;
  font-weight: 600;
  padding: 16px 0;
  border-bottom: 2px solid #e4e7ed;
}

.files-table .el-table__row {
  transition: all 0.3s ease;
}

.files-table .el-table__row:hover {
  background: rgba(64, 158, 255, 0.05);
}

.files-table .folder-row {
  background: rgba(243, 156, 18, 0.02);
}

.files-table .folder-row:hover {
  background: rgba(243, 156, 18, 0.08);
}

.files-table .el-table__row.current-row {
  background: rgba(64, 158, 255, 0.1);
}

/* 表格文件图标 */
.table-file-icon {
  display: flex;
  align-items: center;
  gap: 12px;
}

.table-icon {
  font-size: 20px;
}

.table-thumbnail {
  width: 32px;
  height: 24px;
  border-radius: 4px;
  object-fit: cover;
}

.table-file-name {
  font-weight: 500;
  color: #303133;
}

.table-file-name.is-folder {
  color: #e67e22;
  font-weight: 600;
  cursor: pointer;
}

.table-file-name.is-folder:hover {
  color: #d35400;
  text-decoration: underline;
}

/* === 对话框样式 === */
.upload-dialog .el-dialog__body,
.folder-dialog .el-dialog__body {
  padding: 30px;
}

.upload-area {
  min-height: 200px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(64, 158, 255, 0.02);
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.05);
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  text-align: center;
  line-height: 1.6;
}

/* === 底部状态栏 === */
.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 13px;
  color: #909399;
}

.status-left {
  display: flex;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-icon {
  font-size: 14px;
}

/* === 响应式设计 === */
@media (max-width: 1200px) {
  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 12px;
  }

  .search-input {
    width: 250px;
  }
}

@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 200px;
    order: 2;
  }

  .main-content {
    order: 1;
  }

  .header-bar {
    flex-direction: column;
    gap: 12px;
  }

  .header-left,
  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .search-input {
    width: 200px;
  }

  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 8px;
  }

  .navigation-area {
    flex-direction: column;
    gap: 8px;
  }

  .nav-buttons {
    order: 2;
  }

  .custom-breadcrumb {
    order: 1;
    margin-left: 0;
  }

  .breadcrumb-toolbar {
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .content-inner {
    padding: 12px;
  }

  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }

  .file-card {
    padding: 12px;
  }

  .file-icon-area {
    height: 60px;
  }

  .folder-icon {
    font-size: 36px;
  }

  .file-icon {
    font-size: 30px;
  }

  .file-thumbnail {
    width: 60px;
    height: 45px;
  }
}

/* === 动画效果 === */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-card {
  animation: fadeInUp 0.3s ease forwards;
}

.file-card:nth-child(even) {
  animation-delay: 0.1s;
}

/* 双击提示动画 */
@keyframes doubleClickHint {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.file-card.is-folder:active {
  animation: doubleClickHint 0.3s ease;
}

/* 加载状态优化 */
.content-inner[v-loading] {
  min-height: 400px;
}

/* === 滚动条美化 === */
.sidebar::-webkit-scrollbar,
.content-inner::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.content-inner::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb,
.content-inner::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.content-inner::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* === 辅助类 === */
.text-center {
  text-align: center;
}

.text-muted {
  color: #909399;
}

.font-weight-bold {
  font-weight: 600;
}

/* === 特殊状态 === */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  opacity: 0.8;
}
</style>
